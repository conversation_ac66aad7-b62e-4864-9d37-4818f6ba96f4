/* 分类页面样式模块 - CSS Modules */
@import "../../common/common.less";

/* 页面基础样式 */
:global(page) {
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

/* 搜索头部样式 */
.header {
  position: relative;
  .flex();
  width: 100%;
  min-height: 88rpx;
  background-color: @primary-color;
  padding: 0 20rpx;
  box-sizing: border-box;

  input {
    flex: 1;
    height: 68rpx;
    background: #ffffff;
    border-radius: 34rpx;
    font-size: 28rpx;
    padding: 0 70rpx 0 20rpx;
  }

  .clear {
    position: absolute;
    right: 120rpx;
    color: #ccc;
    font-size: 35rpx;
  }

  .search {
    font-size: 30rpx;
    margin-left: 20rpx;
    color: #fff;
  }
}

/* Tab切换样式 */
.country {
  position: relative;
  min-height: 88rpx;
  border-bottom: 1rpx solid #eee;
}

.openCountry {
  position: fixed;
  left: 0;
  top: 88rpx;
  width: 100%;
  height: auto;
  z-index: 99;
  box-shadow: 0px 14px 14px 0px rgba(0, 0, 0, 0.28);

  .icon {
    transform: rotate(90deg);
  }
}

/* 主内容区域 */
.main {
  flex: 1;
  height: 1px;
  .flex();

  /* 左侧导航 */
  .nav {
    width: 206rpx;
    height: 100%;
    background-color: #f6f6f6;
    font-size: 26rpx;
    color: #0d0d0d;
  }

  .navItem {
    padding-top: 30rpx;
    box-sizing: border-box;

    &:last-child {
      padding-bottom: 30rpx;
    }
  }

  .name {
    .flex(center, center);
    width: 166rpx;
    padding: 0 19rpx;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 10rpx;
    margin: auto;
    font-size: 26rpx;
    border: 5rpx solid #fff;
    color: #525252;
    box-shadow: 0px 4px 11px -6px rgba(0, 0, 0, 0.46);
  }
  .activityCon {
    .activityItem {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 20rpx;

      image {
        width: 48rpx;
        height: 48rpx;
      }

      .name {
        font-size: 28rpx;
        color: #202020;
        font-weight: bold;
        font-style: italic;
      }

      .active {
        color: #99331c;
      }
    }
  }

  .normal {
    height: 65rpx;
    line-height: 65rpx;
    .ellipsis();
  }

  .active {
    height: auto;
    min-height: 65rpx;
    border-color: #156f87;
    color: #156f87;

    .icon {
      transform: rotate(90deg);
    }
  }

  .categoryType {
    display: inline-block;
    font-size: 28rpx;
    font-weight: bold;
    width: 166rpx;
    padding: 30rpx 0 30rpx 40rpx;
    text-align: left;
  }

  .childList {
    text-align: center;

    .child {
      .flex(center, flex-start);
      width: 166rpx;
      padding: 15rpx 0 15rpx 20rpx;
      box-sizing: border-box;
      margin: auto;

      .text {
        display: inline-block;
        flex: 1;
        text-align: left;
        word-break: break-all;
      }

      &.normal {
        .text {
          max-width: 120rpx;
          .ellipsis();
        }
      }
    }
  }

  /* 右侧商品列表 */
  .pro {
    flex: 1;
    height: 100%;
  }

  /* 骨架屏样式 */
  .skeleton {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f8f8f8;

    /* 搜索头部骨架 */
    .skeletonHeader {
      height: 88rpx;
      background-color: #129799;
      padding: 0 20rpx;
      display: flex;
      align-items: center;

      .skeletonSearchInput {
        flex: 1;
        height: 68rpx;
        background: linear-gradient(
          90deg,
          #f0f0f0 25%,
          #e0e0e0 50%,
          #f0f0f0 75%
        );
        background-size: 200% 100%;
        border-radius: 34rpx;
        animation: loading 1.5s infinite;
      }
    }
    @keyframes loading {
      0% {
        background-position: 200% 0;
      }
      100% {
        background-position: -200% 0;
      }
    }

    /* Tab标签栏骨架 */
    .skeletonTabs {
      height: 88rpx;
      background-color: #fff;
      border-bottom: 1rpx solid #eee;
      display: flex;
      align-items: center;
      padding: 0 20rpx;

      .skeletonTabItem {
        width: 120rpx;
        height: 40rpx;
        background: linear-gradient(
          90deg,
          #f0f0f0 25%,
          #e0e0e0 50%,
          #f0f0f0 75%
        );
        background-size: 200% 100%;
        border-radius: 20rpx;
        margin-right: 30rpx;
        animation: loading 1.5s infinite;
        animation-delay: calc(var(--i, 0) * 0.1s);
      }
    }

    /* 主内容区域骨架 */
    .skeletonMain {
      flex: 1;
      display: flex;

      /* 左侧导航骨架 */
      .skeletonNav {
        width: 206rpx;
        background-color: #f6f6f6;
        padding: 30rpx 0;

        .skeletonNavItem {
          margin-bottom: 30rpx;
          display: flex;
          justify-content: center;

          .skeletonNavTitle {
            width: 166rpx;
            height: 65rpx;
            background: linear-gradient(
              90deg,
              #f0f0f0 25%,
              #e0e0e0 50%,
              #f0f0f0 75%
            );
            background-size: 200% 100%;
            border-radius: 10rpx;
            animation: loading 1.5s infinite;
            animation-delay: calc(var(--i, 0) * 0.05s);
          }
        }
      }

      /* 右侧商品列表骨架 */
      .skeletonContent {
        flex: 1;
        background-color: #fff;
        padding: 20rpx;

        .skeletonProductItem {
          display: flex;
          padding: 20rpx;
          margin-bottom: 20rpx;
          background-color: #fff;
          border-radius: 20rpx;
          box-shadow: 0 4rpx 12rpx 1rpx rgba(3, 3, 3, 0.06);

          .skeletonProductImage {
            width: 160rpx;
            height: 160rpx;
            background: linear-gradient(
              90deg,
              #f0f0f0 25%,
              #e0e0e0 50%,
              #f0f0f0 75%
            );
            background-size: 200% 100%;
            border-radius: 10rpx;
            animation: loading 1.5s infinite;
            flex-shrink: 0;
          }

          .skeletonProductInfo {
            flex: 1;
            margin-left: 20rpx;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .skeletonProductTitle {
              width: 100%;
              height: 32rpx;
              background: linear-gradient(
                90deg,
                #f0f0f0 25%,
                #e0e0e0 50%,
                #f0f0f0 75%
              );
              background-size: 200% 100%;
              border-radius: 4rpx;
              animation: loading 1.5s infinite;
              margin-bottom: 12rpx;
            }

            .skeletonProductTitle2 {
              width: 80%;
              height: 32rpx;
              background: linear-gradient(
                90deg,
                #f0f0f0 25%,
                #e0e0e0 50%,
                #f0f0f0 75%
              );
              background-size: 200% 100%;
              border-radius: 4rpx;
              animation: loading 1.5s infinite;
              margin-bottom: 20rpx;
            }

            .skeletonProductPrice {
              display: flex;
              align-items: center;

              .skeletonPriceText {
                width: 80rpx;
                height: 28rpx;
                background: linear-gradient(
                  90deg,
                  #f0f0f0 25%,
                  #e0e0e0 50%,
                  #f0f0f0 75%
                );
                background-size: 200% 100%;
                border-radius: 4rpx;
                animation: loading 1.5s infinite;
                margin-right: 10rpx;
              }

              .skeletonPriceTag {
                width: 60rpx;
                height: 24rpx;
                background: linear-gradient(
                  90deg,
                  #f0f0f0 25%,
                  #e0e0e0 50%,
                  #f0f0f0 75%
                );
                background-size: 200% 100%;
                border-radius: 8rpx;
                animation: loading 1.5s infinite;
              }
            }
          }
        }
      }
    }
  }
}
