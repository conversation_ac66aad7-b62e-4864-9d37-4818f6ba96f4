<!-- 分类页面 - 从WePY2迁移到Taro + Vue3 -->
<template>
  <!-- 骨架屏 -->
  <view v-if="initLoading" :class="styles.skeleton">
    <!-- 搜索头部骨架 -->
    <view :class="styles.skeletonHeader">
      <view :class="styles.skeletonSearchInput"></view>
    </view>

    <!-- Tab标签栏骨架 -->
    <view :class="styles.skeletonTabs">
      <view :class="styles.skeletonTabItem" v-for="n in 3" :key="n"></view>
    </view>

    <!-- 主内容区域骨架 -->
    <view :class="styles.skeletonMain">
      <!-- 左侧导航骨架 -->
      <view :class="styles.skeletonNav">
        <view :class="styles.skeletonNavItem" v-for="n in 8" :key="n">
          <view :class="styles.skeletonNavTitle"></view>
        </view>
      </view>

      <!-- 右侧商品列表骨架 -->
      <view :class="styles.skeletonContent">
        <view :class="styles.skeletonProductItem" v-for="n in 6" :key="n">
          <view :class="styles.skeletonProductImage"></view>
          <view :class="styles.skeletonProductInfo">
            <view :class="styles.skeletonProductTitle"></view>
            <view :class="styles.skeletonProductTitle2"></view>
            <view :class="styles.skeletonProductPrice">
              <view :class="styles.skeletonPriceText"></view>
              <view :class="styles.skeletonPriceTag"></view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <view v-else :class="styles.container">
    <!-- 搜索头部 -->
    <view :class="styles.header" @tap="onGoSearch">
      <input
        type="text"
        disabled
        placeholder="输入你要搜索的内容"
        :value="searchVal"
        :class="styles.h5-input"
      />
      <IconFont
        name="close"
        :class="styles.clear"
        @tap.stop="onClearSearch"
        v-if="searchVal"
      />
    </view>

    <!-- Tab切换 -->
    <nut-tabs
      v-model="tabActiveIndex"
      title-scroll
      :class="styles.country"
      color="#8CDAC6"
      @click="onChangeTab"
    >
      <!-- 服务分类 -->
      <nut-tab-pane
        v-for="(item, i) in serviceList"
        :key="`service-${i}`"
        :title="item.MainCategoryDescrip"
        :pane-key="item.MainCategoryCode"
        :disabled="loading"
      />
      <!-- 国家品牌分类 -->
      <nut-tab-pane
        v-for="(item, i) in countryList"
        :key="`country-${i}`"
        :title="item.CountryBrandDescrip"
        :pane-key="item.CountryBrandCode"
        :disabled="loading"
      />
    </nut-tabs>

    <!-- 主内容区域 -->
    <view
      :class="styles.main"
      v-if="mainH && !initLoading"
      :style="{ marginTop: countryShow ? '88rpx' : '0' }"
    >
      <!-- 左侧分类导航 -->
      <scroll-view :class="styles.nav" scroll-y :scroll-into-view="scrollInto">
        <view
          :class="styles.navItem"
          v-for="(item, i) in category"
          :key="i"
          :id="item.CategoryCode"
        >
          <view
            :class="[
              styles.name,
              currentCategoryIndex === i ? styles.active : styles.normal,
            ]"
            @tap="onChangeCategory(i)"
          >
            {{ item.CategoryDescrip }}
          </view>

          <!-- 子分类列表 -->
          <view
            :class="styles.childList"
            v-if="
              currentCategoryIndex === i &&
              open &&
              item.SubCategory &&
              item.SubCategory.length
            "
          >
            <!-- 服务分类 -->
            <block v-if="item.service && item.service.length > 0">
              <view
                v-for="(item2, i2) in item.service"
                :key="i2"
                :class="[
                  styles.child,
                  currentSubCategoryCode === item2.SubCategoryCode
                    ? styles.active
                    : styles.normal,
                ]"
                @tap="onChangeCategory2(item2.SubCategoryCode, '')"
              >
                <view :class="styles.text">{{ item2.SubCategoryDescrip }}</view>
                <IconFont name="arrow-right" :class="styles.icon" />
              </view>
            </block>

            <!-- 产品分类 -->
            <block v-if="item.other && item.other.length > 0">
              <view :class="styles.categoryType">产品分类</view>
              <view
                v-for="(item2, i2) in item.other"
                :key="i2"
                :class="[
                  styles.child,
                  currentSubCategoryCode === item2.SubCategoryCode
                    ? styles.active
                    : styles.normal,
                ]"
                @tap="onChangeCategory2(item2.SubCategoryCode, '')"
              >
                <view :class="styles.text">{{ item2.SubCategoryDescrip }}</view>
                <IconFont name="arrow-right" :class="styles.icon" />
              </view>
            </block>

            <!-- 品牌分类 -->
            <block v-if="item.brand && item.brand.length > 0">
              <view :class="styles.categoryType">品牌分类</view>
              <view
                v-for="(item2, i2) in item.brand"
                :key="i2"
                :class="[
                  styles.child,
                  BrandDescrip === item2.BrandDescrip
                    ? styles.active
                    : styles.normal,
                ]"
                @tap="onChangeCategory2('', item2.BrandDescrip)"
              >
                <view :class="styles.text">{{ item2.BrandDescrip }}</view>
                <IconFont name="arrow-right" :class="styles.icon" />
              </view>
            </block>
          </view>
        </view>
      </scroll-view>

      <!-- 右侧商品列表 -->
      <scroll-view
        :class="styles.pro"
        :scroll-y="true"
        :scroll-anchoring="true"
        :refresher-enabled="true"
        :refresher-triggered="triggered"
        @refresherrefresh="onRefresh"
        @scrolltolower="onLoadMore"
      >
        <!-- 空状态 -->
        <Empty
          text="暂无商品数据~"
          v-if="loadType === 'done' && productList.length === 0"
        />

        <!-- 商品列表 -->
        <view v-for="(item, i) in productList" :key="i">
          <ProItem
            size="small"
            :custom-style="'margin: 20rpx 20rpx 0'"
            :data="item"
            :hover="true"
            @tap="goDetail"
            @addCart="onAddCart"
          />
        </view>

        <!-- 加载更多 -->
        <LoadMoreBottom
          v-if="loadType === 'loading' || productList.length > 0"
          :type="loadType"
          @refresh="getProList"
        />
      </scroll-view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from "vue";
import Taro, { useLoad, usePullDownRefresh, useDidShow } from "@tarojs/taro";
// 引入API服务 - 从WePY项目迁移
import {
  getProductList,
  CountryBrand,
  CountryBrandCategory,
  CategoryBrand,
  ServiceHomePageMainCategory,
  ServiceMainCatgCategory,
  Service,
} from "../../services/api";
// 引入常量
import { TRADE_CATEGORY } from "../../constants";
// 引入组件
import Empty from "../../components/empty/index.vue";
import LoadMoreBottom from "../../components/loadMoreBottom/index.vue";
import ProItem from "../../components/productList/item/index.vue";
import styles from "./index.module.less";

// 响应式数据定义 - 从WePY项目迁移
const activityIndex = ref("");
const searchVal = ref("");
const triggered = ref(false);
const mainH = ref(0);
const initLoading = ref(false);
const loading = ref(false);
const scrollInto = ref("");
const category = ref<any[]>([]); // 侧边栏分类列表
const currentCategoryIndex = ref(0); // 一级分类当前索引
const currentSubCategoryCode = ref(""); // 二级分类当前code
const BrandDescrip = ref(""); // 当前选择的品牌名称
const open = ref(true); // 一级分类是否展开
const loadType = ref("loading");
const page = ref(1);
const pageSize = ref(10);
const productList = ref<any[]>([]);
const total = ref(0);
const countryList = ref<any[]>([]); // 国家品牌列表
const serviceList = ref<any[]>([]); // 服务主分类列表
const tabActiveCode = ref<string | null>(null); // tab当前激活的code
const tabActiveIndex = ref(0); // tab当前激活的索引
const currTabType = ref("country"); // 当前主分类类型
const countryShow = ref(false);

// 计算属性
const allTabList = computed(() => [...serviceList.value, ...countryList.value]);

// 方法定义 - 从WePY项目迁移

// 商品详情跳转
const goDetail = (data: any) => {
  const {
    ProductCode,
    PreGroupStartDate,
    PreLimitedStartDate,
    PreGroupReferNo,
    PreLimitedReferNo,
    Product,
  } = data;
  const now = Date.now();
  const isService = Product === "False";

  const navigateToDetail = (promoType: string, referNo: string) => {
    Taro.navigateTo({
      url: `/pages/proDetail/index?ProductCode=${ProductCode}&PromoType=${promoType}&type=pre&ReferNo=${referNo}&isService=${isService}`,
    });
  };

  const isPreGroupActive =
    PreGroupStartDate &&
    PreGroupStartDate !== "-" &&
    new Date(PreGroupStartDate.replace(/-/g, "/")).getTime() > now;
  const isPreLimitedActive =
    PreLimitedStartDate &&
    PreLimitedStartDate !== "-" &&
    new Date(PreLimitedStartDate.replace(/-/g, "/")).getTime() > now;

  if (isPreGroupActive) {
    navigateToDetail("GP", PreGroupReferNo);
  } else if (isPreLimitedActive) {
    navigateToDetail("LT", PreLimitedReferNo);
  } else {
    Taro.navigateTo({
      url: `/pages/proDetail/index?ProductCode=${ProductCode}${
        isService ? `&isService=${isService}` : ""
      }`,
    });
  }
};

// 添加到购物车
const onAddCart = (data: any) => {
  // 这里可以添加购物车逻辑
  console.log("添加到购物车:", data);
  Taro.showToast({
    title: "已添加到购物车",
    icon: "success",
    duration: 2000,
  });
};

// 跳转搜索页面
const onGoSearch = () => {
  Taro.navigateTo({
    url: "/subpackages/pages/searchPro/index",
  });
};

// 清除搜索内容
const onClearSearch = () => {
  searchVal.value = "";
};

// 获取国家品牌
const getCountry = async () => {
  try {
    const res = await CountryBrand();
    const d = res.data.d;
    if (d) {
      countryList.value = d || [];
    }
  } catch (error) {
    console.error("获取国家品牌失败:", error);
  }
};

// 获取首页服务主分类栏目
const getServiceHomePageMainCategory = async () => {
  try {
    const res = await ServiceHomePageMainCategory();
    const d = res.data.d;
    if (d) {
      serviceList.value = d || [];
    }
  } catch (error) {
    console.error("获取服务主分类失败:", error);
  }
};

// 格式化分类数据
const formatCategory = (data: any[], type = "product") => {
  return data.map((item) => {
    if (item.SubCategory?.length) {
      if (type === "service") {
        item.service = item.SubCategory;
      } else {
        const [brand, other] = item.SubCategory.reduce(
          (acc: any[], subItem: any) => {
            const targetArray =
              ["p", "P"].includes(subItem.SubCategoryCode[0]) ||
              subItem.SubCategoryCode.includes("Three")
                ? acc[0]
                : acc[1];
            targetArray.push(subItem);
            return acc;
          },
          [[], []]
        );
        item.brand = brand;
        item.other = other;
      }
    }
    return item;
  });
};

// 获取分类品牌
const getCategoryBrand = async () => {
  const curr = category.value[currentCategoryIndex.value];
  try {
    const res = await CategoryBrand({ CategoryCode: curr.CategoryCode });
    if (res.data.d) {
      category.value[currentCategoryIndex.value].brand = res.data.d || [];
    }
  } catch (error) {
    console.error("获取分类品牌失败:", error);
  }
};

// 获取国家分类
const getCountryCategory = async () => {
  Taro.showLoading({ title: "加载中...", mask: true });
  try {
    const res = await CountryBrandCategory({
      CountryBrandCode: tabActiveCode.value,
    });
    if (res.data.d && res.data.d.length) {
      const list = formatCategory(res.data.d);
      category.value = list;
      currentCategoryIndex.value = 0;
      await getCategoryBrand();
      currentSubCategoryCode.value = list[0]?.other?.[0]?.SubCategoryCode || "";
      if (currentSubCategoryCode.value === "")
        BrandDescrip.value = list[0]?.brand?.[0]?.BrandDescrip || "";
      page.value = 1;
      productList.value = [];
      getProList();
    } else {
      category.value = [];
      productList.value = [];
    }
  } catch (error) {
    console.error("获取国家分类失败:", error);
  } finally {
    Taro.hideLoading();
  }
};

// 获取服务主分类
const getServiceMainCatgCategory = async () => {
  Taro.showLoading({ title: "加载中...", mask: true });
  try {
    const res = await ServiceMainCatgCategory({
      MainCategoryCode: tabActiveCode.value,
    });
    if (res.data.d && res.data.d.length) {
      const list = formatCategory(res.data.d, "service");
      category.value = list;
      currentCategoryIndex.value = 0;
      currentSubCategoryCode.value =
        list[0]?.service?.[0]?.SubCategoryCode || "";
      BrandDescrip.value = "";
      page.value = 1;
      productList.value = [];
      getProList();
    } else {
      category.value = [];
      productList.value = [];
    }
  } catch (error) {
    console.error("获取服务主分类失败:", error);
  } finally {
    Taro.hideLoading();
  }
};

// 切换主分类tab
const onChangeTab = (paneKey: string) => {
  const allList = allTabList.value;
  const index = allList.findIndex(
    (item) =>
      item.MainCategoryCode === paneKey || item.CountryBrandCode === paneKey
  );

  if (index === -1) return;

  const item = allList[index];
  const type = item.MainCategoryCode ? "service" : "country";

  currTabType.value = type;
  open.value = true;
  tabActiveCode.value = paneKey;
  tabActiveIndex.value = index;
  productList.value = [];

  if (type === "service") {
    getServiceMainCatgCategory();
  } else {
    getCountryCategory();
  }
};

// 获取商品列表
const getProList = async () => {
  if (category.value.length === 0) return;

  let data: any = {
    Page: page.value,
    PageSize: pageSize.value,
    BrandDescrip: BrandDescrip.value,
  };

  const curr = category.value[currentCategoryIndex.value];
  if (curr.SubCategory && !BrandDescrip.value) {
    data.CategoryCode = currentSubCategoryCode.value;
    data.CategoryLevel = 2;
  } else {
    data.CategoryCode = curr.CategoryCode;
    data.CategoryLevel = 1;
  }

  if (currTabType.value === "service") {
    data.SupplierCountry = "";
    data.AreaCode = "";
    data.City = "";
    data.District = "";
  }

  Taro.showNavigationBarLoading();
  loading.value = true;
  loadType.value = "loading";

  try {
    const fn = currTabType.value === "service" ? Service : getProductList;
    const res = await fn(data);
    const d = res.data.d;

    if (d && Array.isArray(d)) {
      productList.value = page.value === 1 ? d : [...productList.value, ...d];
      total.value = (d[0] && d[0].TotalProduct) || 0;

      if (productList.value.length >= total.value) {
        loadType.value = "done";
      } else {
        page.value = page.value + 1;
      }
    } else {
      loadType.value = "error";
    }
  } catch (error) {
    console.error("获取商品列表失败:", error);
    loadType.value = "error";
  } finally {
    triggered.value = false;
    loading.value = false;
    Taro.hideNavigationBarLoading();
    initLoading.value = false;
  }
};

// 加载更多
const onLoadMore = () => {
  if (loadType.value === "done") return false;
  getProList();
};

// 下拉刷新
const onRefresh = () => {
  page.value = 1;
  triggered.value = true;
  getProList();
};

// 切换分类
const onChangeCategory = async (index: number) => {
  if (loading.value) return;

  if (index === currentCategoryIndex.value) {
    open.value = !open.value;
    return;
  }

  BrandDescrip.value = "";
  activityIndex.value = "";
  open.value = true;
  currentCategoryIndex.value = index;

  if (!category.value[index]?.service) {
    await getCategoryBrand(); // 服务不请求此接口
  }

  productList.value = [];
  page.value = 1;

  if (category.value[index]?.service) {
    currentSubCategoryCode.value =
      category.value[index]?.service?.[0]?.SubCategoryCode || "";
  } else {
    currentSubCategoryCode.value =
      category.value[index]?.other?.[0]?.SubCategoryCode || "";
  }

  if (currentSubCategoryCode.value === "") {
    BrandDescrip.value = category.value[index]?.brand?.[0]?.BrandDescrip || "";
  }

  scrollInto.value = category.value[index].CategoryCode;
  getProList();
};

// 切换二级分类
const onChangeCategory2 = (code: string, brandDescrip: string) => {
  if (loading.value) return;
  if (code && code === currentSubCategoryCode.value) return;
  if (brandDescrip && brandDescrip === BrandDescrip.value) return;

  currentSubCategoryCode.value = code;
  BrandDescrip.value = brandDescrip;
  productList.value = [];
  page.value = 1;
  getProList();
};

// 初始化
const init = async () => {
  initLoading.value = true;
  Taro.showLoading({ title: "加载中...", mask: true });

  try {
    await getServiceHomePageMainCategory();
    await getCountry();

    if (serviceList.value.length > 0) {
      currTabType.value = "service";
      tabActiveCode.value = serviceList.value[0].MainCategoryCode;
      tabActiveIndex.value = 0;
      getServiceMainCatgCategory();
    } else if (countryList.value.length > 0) {
      currTabType.value = "country";
      tabActiveCode.value = countryList.value[0].CountryBrandCode;
      tabActiveIndex.value = serviceList.value.length;
      getCountryCategory();
    }
  } catch (error) {
    console.error("初始化失败:", error);
  } finally {
    Taro.hideLoading();
  }
};

// 生命周期钩子
useLoad(() => {
  // 计算主内容区域高度
  const query = Taro.createSelectorQuery();
  query
    .select(".container")
    .boundingClientRect((rect) => {
      if (rect && !Array.isArray(rect) && rect.height) {
        mainH.value = rect.height - 88;
      }
    })
    .exec();

  init();
});

// 下拉刷新
usePullDownRefresh(() => {
  onRefresh();
});

// 页面显示时
useDidShow(() => {
  // 可以在这里添加页面显示时的逻辑
});
</script>
